/**
 * 请求封装工具
 * 统一处理API前缀、请求头、Token携带、错误码处理和loading等逻辑
 * 支持token过期自动重试机制
 */

// 直接使用 uni-app 提供的全局 getApp()，无需重新定义

// 导入配置文件
import { baseUrl as configBaseUrl } from './config.js';

// 请求重试管理器
class RequestRetryManager {
	constructor() {
		this.retryQueue = new Map(); // 存储重试请求
		this.isRefreshingToken = false; // 是否正在刷新token
		this.refreshTokenPromise = null; // token刷新Promise
		this.maxRetries = 3; // 最大重试次数
		this.retryDelays = [1000, 2000, 4000]; // 重试延迟时间(ms)
	}

	// 添加请求到重试队列
	addToRetryQueue(requestConfig, resolve, reject, retryCount = 0) {
		const requestId = this.generateRequestId(requestConfig);
		this.retryQueue.set(requestId, {
			config: requestConfig,
			resolve,
			reject,
			retryCount,
			timestamp: Date.now()
		});
		return requestId;
	}

	// 生成请求ID
	generateRequestId(config) {
		return `${config.method || 'GET'}_${config.url}_${Date.now()}_${Math.random()}`;
	}

	// 清理过期的重试请求
	cleanExpiredRequests() {
		const now = Date.now();
		const expireTime = 5 * 60 * 1000; // 5分钟过期

		for (const [requestId, request] of this.retryQueue.entries()) {
			if (now - request.timestamp > expireTime) {
				request.reject(new Error('请求超时'));
				this.retryQueue.delete(requestId);
			}
		}
	}

	// 重试队列中的所有请求
	async retryQueuedRequests() {
		const requests = Array.from(this.retryQueue.values());
		this.retryQueue.clear();

		for (const request of requests) {
			try {
				// 延迟重试，避免并发过多
				await new Promise(resolve => setTimeout(resolve, 100));
				const result = await this.executeRequest(request.config);
				request.resolve(result);
			} catch (error) {
				// 如果重试次数未达到上限，重新加入队列
				if (request.retryCount < this.maxRetries - 1) {
					const delay = this.retryDelays[request.retryCount] || 4000;
					setTimeout(() => {
						this.retryRequest(request.config, request.resolve, request.reject, request.retryCount + 1);
					}, delay);
				} else {
					request.reject(error);
				}
			}
		}
	}

	// 重试单个请求
	async retryRequest(config, resolve, reject, retryCount) {
		try {
			const result = await this.executeRequest(config);
			resolve(result);
		} catch (error) {
			if (retryCount < this.maxRetries - 1) {
				const delay = this.retryDelays[retryCount] || 4000;
				setTimeout(() => {
					this.retryRequest(config, resolve, reject, retryCount + 1);
				}, delay);
			} else {
				reject(error);
			}
		}
	}

	// 执行请求
	executeRequest(config) {
		return new Promise((resolve, reject) => {
			uni.request({
				...config,
				success: (res) => {
					if (res.statusCode >= 200 && res.statusCode < 300) {
						resolve(res.data);
					} else {
						reject(res);
					}
				},
				fail: reject
			});
		});
	}

	// 刷新token
	async refreshToken() {
		if (this.isRefreshingToken && this.refreshTokenPromise) {
			return this.refreshTokenPromise;
		}

		this.isRefreshingToken = true;
		this.refreshTokenPromise = this.performTokenRefresh();

		try {
			const result = await this.refreshTokenPromise;
			this.isRefreshingToken = false;
			this.refreshTokenPromise = null;
			return result;
		} catch (error) {
			this.isRefreshingToken = false;
			this.refreshTokenPromise = null;
			throw error;
		}
	}

	// 执行token刷新
	async performTokenRefresh() {
		const app = getApp();

		console.log('开始执行token刷新流程');

		// 清除旧token和用户信息
		uni.removeStorageSync('token');
		app.globalData.isLoggedIn = false;
		app.globalData.userInfo = null;

		// 调用强制刷新登录
		app.autoLogin(true);

		// 等待登录完成
		const loginResult = await app.globalData.loginPromise;

		if (!loginResult.success) {
			console.error('Token刷新失败:', loginResult.message);
			throw new Error(loginResult.message || '重新登录失败');
		}

		console.log('Token刷新成功');
		return loginResult;
	}
}

// 创建全局重试管理器实例
const retryManager = new RequestRetryManager();

/**
 * 请求封装（支持自动重试）
 * @param {Object} options - 请求选项
 * @param {string} options.url - 接口地址（无需添加前缀）
 * @param {string} [options.method='GET'] - 请求方法
 * @param {Object} [options.data] - 请求数据
 * @param {boolean} [options.showLoading=true] - 是否显示加载提示
 * @param {boolean} [options.hideErrorTips=false] - 是否隐藏错误提示
 * @param {boolean} [options.enableRetry=true] - 是否启用自动重试
 * @param {number} [options._retryCount=0] - 内部重试计数器
 * @returns {Promise} 返回Promise对象
 */
const request = function(options) {
	// 默认配置
	const config = {
		baseUrl: getApp().globalData.apiBaseUrl || configBaseUrl,
		// 默认不显示加载提示，只有明确指定时才显示
		showLoading: options.showLoading === true,
		hideErrorTips: options.hideErrorTips === true,
		enableRetry: options.enableRetry !== false, // 默认启用重试
		_retryCount: options._retryCount || 0, // 内部重试计数器
		...options
	};

	// 拼接完整URL
	if (config.url.indexOf('http') !== 0) {
		config.url = config.baseUrl + config.url;
	}

	// 显示加载提示（只在首次请求时显示）
	if (config.showLoading && config._retryCount === 0) {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
	}

	// 获取Token
	const token = uni.getStorageSync('token');

	// 构建请求头
	config.header = {
		'Content-Type': 'application/json',
		...config.header
	};

	// 如果有Token，添加到请求头
	if (token) {
		config.header['Authorization'] = `Bearer ${token}`;
	}

	// 发起请求并返回Promise
	return new Promise(async (resolve, reject) => {
		uni.request({
			url: config.url,
			data: config.data,
			method: config.method || 'GET',
			header: config.header,
			success: async function(res) {
				// 请求成功
				if (res.statusCode >= 200 && res.statusCode < 300) {
					// 隐藏加载提示
					if (config.showLoading) {
						uni.hideLoading();
					}
					resolve(res.data);
				}
				// 未授权 - 启用自动重试机制
				else if (res.statusCode === 401 && config.enableRetry) {
					console.log(`Token过期，开始自动重试流程 (第${config._retryCount + 1}次)`);

					try {
						// 刷新token
						await retryManager.refreshToken();

						// 重新发起请求
						const retryConfig = {
							...config,
							_retryCount: config._retryCount + 1
						};

						// 如果重试次数超过限制，禁用进一步重试
						if (retryConfig._retryCount >= retryManager.maxRetries) {
							retryConfig.enableRetry = false;
						}

						const result = await request(retryConfig);
						resolve(result);

					} catch (retryError) {
						console.error('自动重试失败:', retryError);

						// 隐藏加载提示
						if (config.showLoading) {
							uni.hideLoading();
						}

						// 重试失败，显示错误提示
						if (!config.hideErrorTips) {
							uni.showToast({
								title: config._retryCount >= retryManager.maxRetries ?
									'登录失败，请下拉刷新重试' : '登录已过期，正在重试...',
								icon: 'none',
								duration: 2000
							});
						}

						reject(res);
					}
				}
				// 未授权但不启用重试，或重试次数已达上限
				else if (res.statusCode === 401) {
					// 隐藏加载提示
					if (config.showLoading) {
						uni.hideLoading();
					}

					// Token无效或过期，清除本地Token
					uni.removeStorageSync('token');
					getApp().globalData.isLoggedIn = false;

					if (!config.hideErrorTips) {
						uni.showToast({
							title: '登录已过期，请下拉刷新重试',
							icon: 'none',
							duration: 3000
						});
					}

					reject(res);
				}
				// 哇图币不足
				else if (res.statusCode === 402) {
					// 隐藏加载提示
					if (config.showLoading) {
						uni.hideLoading();
					}

					if (!config.hideErrorTips) {
						uni.showModal({
							title: '哇图币不足',
							content: res.data.detail || '您的哇图币余额不足，无法完成此操作',
							confirmText: '去获取',
							cancelText: '取消',
							success: (result) => {
								if (result.confirm) {
									// 跳转到支付页面获取哇图币
									uni.navigateTo({
										url: '/pages/pay/pay'
									});
								}
							}
						});
					}
					reject(res);
				}
				// 其他错误
				else {
					// 隐藏加载提示
					if (config.showLoading) {
						uni.hideLoading();
					}

					if (!config.hideErrorTips) {
						uni.showToast({
							title: res.data.detail || '请求失败',
							icon: 'none',
							duration: 2000
						});
					}
					reject(res);
				}
			},
			fail: function(err) {
				// 隐藏加载提示
				if (config.showLoading) {
					uni.hideLoading();
				}

				if (!config.hideErrorTips) {
					uni.showToast({
						title: '网络请求失败',
						icon: 'none',
						duration: 2000
					});
				}

				reject(err);
			}
		});
	});
};

// 快捷方法
const get = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'GET',
		...options
	});
};

const post = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'POST',
		...options
	});
};

// 添加PUT方法
const put = function(url, data, options = {}) {
	return request({
		url,
		data,
		method: 'PUT',
		...options
	});
};

// 导出请求方法
export default {
	request,
	get,
	post,
	put,
	baseUrl: configBaseUrl // 导出baseUrl便于上传文件等场景使用
}; 