<template>
	<!-- 骨架屏加载动画 -->
	<skeleton-loading v-if="isLoading" @load-complete="onSkeletonLoadComplete"></skeleton-loading>
	
	<!-- 实际内容，登录成功后显示 -->
	<scroll-view v-else class="container no-scrollbar" scroll-y="true" show-scrollbar="false"
		refresher-enabled="true" :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">
		<!-- 顶部安全区域 -->
		<view class="safe-area"></view>
		
		<!-- 头部用户信息，只保留问候语 -->
		<view class="header">
			<view class="user-info">
				<image class="avatar" :src="userInfo.avatar_url ? getImageUrl(userInfo.avatar_url) : '/static/头像.png'"></image>
				<view class="greeting">
					<text>嗨，{{ userInfo.nickname || '哇兔兔' }}!</text>
				</view>
			</view>
		</view>
		
		<!-- 风格探索区块 - 只保留标题 -->
		<view class="section-container">
			<view class="fancy-section-header">
				<view class="fancy-line"></view>
				<text class="fancy-title">风格探索</text>
			</view>
			
			<!-- 顶部横向滚动风格展示 -->
			<scroll-view class="style-scroll" scroll-x="true" show-scrollbar="false" enhanced="true">
				<view class="style-scroll-content">
					<view class="scroll-style-card" v-for="(item, index) in aiTools.slice(0, 4)" :key="index" 
						@click="navigateToGenerate(item)">
						<image class="scroll-style-image" :src="getImageUrl(item.image)" mode="aspectFill"></image>
						<view class="scroll-style-overlay"></view>
						<text class="scroll-style-name">{{item.name}}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 活动推荐区块 - 只保留标题 -->
		<view class="section-container">
			<view class="fancy-section-header alt-color">
				<view class="fancy-line"></view>
				<text class="fancy-title">活动推荐</text>
			</view>
			
			<!-- 中部Banner活动入口 -->
			<view class="mini-banner" v-if="bannerList && bannerList.length > 0" 
				@click="onBannerClick(bannerList[currentBannerIndex].id)">
				<swiper class="mini-banner-swiper" 
					:circular="true" 
					:autoplay="true" 
					:interval="4000" 
					:duration="500"
					:indicator-dots="false"
					@change="onSwiperChange">
					<swiper-item v-for="banner in bannerList" :key="banner.id">
						<view class="mini-banner-content" 
							:style="{backgroundImage: `url(${getImageUrl(banner.image_url)})`}">
							<view class="mini-banner-text">
								<text class="mini-banner-title">{{ banner.title || '新功能上线' }}</text>
								<text class="mini-banner-subtitle">{{ banner.description || '点击查看详情' }}</text>
							</view>
						</view>
					</swiper-item>
				</swiper>
				
				<!-- 自定义指示点 -->
				<view class="mini-banner-indicators">
					<view v-for="(_, index) in bannerList" :key="index" 
						class="mini-indicator" :class="{ 'active': currentBannerIndex === index }"></view>
				</view>
			</view>
		</view>
		
		<!-- 创意灵感区块 - 只保留标题 -->
		<view class="section-container">
			<view class="fancy-section-header">
				<view class="fancy-line"></view>
				<text class="fancy-title">创意灵感</text>
			</view>
			
			<!-- 瀑布流风格展示 -->
			<view class="style-waterfall">
				<!-- 左列 -->
				<view class="waterfall-column">
					<view class="waterfall-item" v-for="(item, index) in waterfall.left" :key="index" 
						@click="navigateToGenerate(item)">
						<!-- 如果有多张图片，则使用轮播图 -->
						<swiper v-if="item.sliderImages && item.sliderImages.length > 1" 
							class="waterfall-swiper"
							:circular="true" 
							:autoplay="true" 
							:interval="3000" 
							:duration="500"
							:indicator-dots="true"
							indicator-color="rgba(255,255,255,0.4)"
							indicator-active-color="#FFFFFF">
							<swiper-item v-for="(img, imgIndex) in item.sliderImages" :key="imgIndex">
								<image :class="['waterfall-image', { crop: item.useCrop }]" :src="getImageUrl(img)" :mode="item.useCrop ? 'aspectFill' : 'widthFix'"></image>
							</swiper-item>
						</swiper>
						<!-- 单图显示 -->
						<image v-else :class="['waterfall-image', { crop: item.useCrop }]" :src="getImageUrl(item.image)" :mode="item.useCrop ? 'aspectFill' : 'widthFix'"></image>
						<view class="waterfall-info">
							<text class="waterfall-desc">{{item.desc}}</text>
							<text class="waterfall-name">{{item.name}}</text>
							<!-- 热门标识 -->
							<view class="waterfall-hot" v-if="item.isPopular">
								<text class="hot-text">HOT</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 右列 -->
				<view class="waterfall-column">
					<view class="waterfall-item" v-for="(item, index) in waterfall.right" :key="index" 
						@click="navigateToGenerate(item)">
						<!-- 如果有多张图片，则使用轮播图 -->
						<swiper v-if="item.sliderImages && item.sliderImages.length > 1" 
							class="waterfall-swiper"
							:circular="true" 
							:autoplay="true" 
							:interval="3000" 
							:duration="500"
							:indicator-dots="true"
							indicator-color="rgba(255,255,255,0.4)"
							indicator-active-color="#FFFFFF">
							<swiper-item v-for="(img, imgIndex) in item.sliderImages" :key="imgIndex">
								<image :class="['waterfall-image', { crop: item.useCrop }]" :src="getImageUrl(img)" :mode="item.useCrop ? 'aspectFill' : 'widthFix'"></image>
							</swiper-item>
						</swiper>
						<!-- 单图显示 -->
						<image v-else :class="['waterfall-image', { crop: item.useCrop }]" :src="getImageUrl(item.image)" :mode="item.useCrop ? 'aspectFill' : 'widthFix'"></image>
						<view class="waterfall-info">
							<text class="waterfall-desc">{{item.desc}}</text>
							<text class="waterfall-name">{{item.name}}</text>
							<!-- 热门标识 -->
							<view class="waterfall-hot" v-if="item.isPopular">
								<text class="hot-text">HOT</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部安全区域 -->
		<view class="bottom-safe-area"></view>
	</scroll-view>

	<!-- 导入公告弹窗组件 -->
	<notice-modal :show="showNotice" :noticeData="noticeData" @close="closeNotice"></notice-modal>
</template>

<script>
	import request from '../../utils/request.js'
	import SkeletonLoading from '../../components/SkeletonLoading.vue'
	import NoticeModal from '../../components/NoticeModal.vue'
	import { getImageUrl } from '../../utils/config.js'

	export default {
		components: {
			SkeletonLoading,
			NoticeModal
		},
		data() {
			return {
				aiTools: [
					{
						name: '吉卜力风格',
						desc: '闯入动漫世界',
						image: '/static/logo.png'
					},
					{
						name: '猫咪去旅行',
						desc: '带主子环游世界',
						image: '/static/logo.png',
						// 为猫咪去旅行添加轮播图片
						sliderImages: [
							'/static/styles/cat_travel.png',
							'/static/styles/cat_travel1.png'
						]
					},
					{
						name: 'AI 艺术头像',
						desc: '百变风格写真',
						image: '/static/logo.png'
					},
					{
						name: '名人合照',
						desc: '圆你的追星梦',
						image: '/static/logo.png'
					}
				],
				userInfo: {
					avatar_url: '/static/头像.png',
					nickname: '哇兔兔',
					coins: 0
				},
				bannerList: [],
				currentBannerIndex: 0,
				isLoading: true,  // 控制骨架屏显示
				isRefreshing: false, // 控制下拉刷新状态
				banners: [],
				styles: [], // 风格列表
				// 瀑布流数据结构
				waterfall: {
					left: [],
					right: []
				},
				// 公告相关数据
				showNotice: false,
				noticeData: null
			}
		},
		onLoad() {
			// 骨架屏会自动处理用户信息加载，这里只需要等待
			console.log('首页加载开始，等待骨架屏完成用户信息加载');
		},
		onShow() {
			// 页面显示时，如果不是首次加载且已登录，则刷新用户信息
			if (!this.isLoading) {
				const app = getApp();
				if (app.globalData.isLoggedIn) {
					this.loadUserInfo(true);
				}
			}
		},
		onShareAppMessage() {
			return {
				title: '人，猫生是旷野！！！',
				path: '/pages/index/index',
				imageUrl: '/static/share.jpg'
			};
		},
		onShareTimeline() {
			return {
				title: '人，猫生是旷野！！！',
				imageUrl: '/static/share.jpg'
			};
		},
		methods: {
			// 直接使用导入的getImageUrl函数
			getImageUrl,

			// 骨架屏加载完成回调
			async onSkeletonLoadComplete(result) {
				console.log('骨架屏加载完成:', result);

				if (result.success) {
					// 用户信息加载成功，更新本地数据
					this.userInfo = result.userInfo;

					// 继续加载其他数据（启用自动重试）
					try {
						await Promise.all([
							this.loadBanners(),
							this.loadStyles(),
							this.loadNotice()
						]);
						console.log('所有数据加载完成');
					} catch (error) {
						console.error('其他数据加载失败:', error);
						// 即使其他数据加载失败，也要显示首页
						// 由于启用了自动重试，这里的错误提示更温和
						uni.showToast({
							title: '部分数据加载失败',
							icon: 'none',
							duration: 1500
						});
					}
				} else {
					// 用户信息加载失败
					console.error('用户信息加载失败:', result.error);

					// 由于已经启用了自动重试机制，这里的提示更加友好
					// 不再强调"下拉刷新"，因为系统会自动处理
					uni.showToast({
						title: result.error || '加载失败，系统正在重试',
						icon: 'none',
						duration: 2000
					});
				}

				// 无论成功失败，都隐藏骨架屏，显示首页
				this.isLoading = false;
			},
			
			// 下拉刷新
			onRefresh() {
				console.log('下拉刷新触发');
				this.isRefreshing = true;
				
				// 重新加载数据
				Promise.all([
					this.loadUserInfo(true),
					this.loadBanners(),
					this.loadStyles()
				]).finally(() => {
					this.isRefreshing = false;
				});
			},
			
			// 初始化瀑布流数据
			initWaterfall(styles) {
				this.waterfall = {
					left: [],
					right: []
				};
				
				if (styles && styles.length > 0) {
					// 分配到左右两列
					styles.forEach((style, index) => {
						// 处理示例图数组，过滤空值
						const sliderArr = Array.isArray(style.example_images) ? style.example_images.filter(img => !!img) : [];
						// 若存在示例图，优先展示第一张；否则使用封面图
						const coverImg = sliderArr.length > 0 ? sliderArr[0] : (style.cover_image_url || '/static/logo.png');
						
						const useCrop = sliderArr.length > 1; // 多张示例图可能尺寸不一，需要裁剪

						const item = {
							id: style.id,
							name: style.name,
							desc: style.description,
							// image 用于单图展示或 sliderImages 只有一张时
							image: coverImg,
							// 多张示例图时用于轮播
							sliderImages: sliderArr.length > 0 ? sliderArr : null,
							// 是否需要裁剪（多图）
							useCrop: useCrop,
							// 添加热门标记（权重>0 视为热门）
							isPopular: style.is_popular > 0
						};
						
						// 偶数放左边，奇数放右边
						if (index % 2 === 0) {
							this.waterfall.left.push(item);
						} else {
							this.waterfall.right.push(item);
						}
					});
				}
			},
			

			
			// 加载用户信息
			async loadUserInfo(force = false) {
				const app = getApp();
				// 当 force 为 true 时，忽略缓存，重新向后端请求最新数据
				if (!force && app.globalData.userInfo) {
					this.userInfo = app.globalData.userInfo;
					return;
				}

				try {
					const res = await request.get('/wowpic/auth/me', null, {
						showLoading: false,
						enableRetry: true // 启用自动重试
					});
					this.userInfo = res;
					app.globalData.userInfo = res;
				} catch (err) {
					console.error('获取用户信息失败', err);
					throw err; // 向上传递错误
				}
			},
			
			// 加载公告
			async loadNotice() {
				try {
					const res = await request.get('/wowpic/notices', {
						display_type: 'home_page'  // 明确指定获取首页公告
					}, {
						showLoading: false,
						enableRetry: true // 启用自动重试
					});
					console.log('获取首页公告结果:', res);

					if (res && res.has_notice && res.notice) {
						this.noticeData = res.notice;
						this.showNotice = true;
					} else {
						this.noticeData = null;
						this.showNotice = false;
					}
				} catch (err) {
					console.error('获取首页公告失败', err);
					this.noticeData = null;
					this.showNotice = false;
				}
			},
			
			// 关闭公告
			closeNotice(noticeId) {
				this.showNotice = false;
				if (noticeId) {
					uni.setStorageSync('notice_' + noticeId + '_showed', true);
				}
			},
			
			// 加载Banner
			async loadBanners() {
				try {
					const res = await request.get('/wowpic/banners', null, {
						showLoading: false,
						enableRetry: true // 启用自动重试
					});
					this.bannerList = res;
				} catch (err) {
					console.error('获取Banner失败', err);
					throw err; // 向上传递错误
				}
			},
			
			// 加载热门风格
			async loadStyles() {
				try {
					// 先获取原始数据（启用自动重试）
					const [popularStylesRaw, allStylesRaw] = await Promise.all([
						request.get('/wowpic/styles/popular', null, {
							showLoading: false,
							enableRetry: true
						}),
						request.get('/wowpic/styles', null, {
							showLoading: false,
							enableRetry: true
						})
					]);

					// 过滤掉通用自由创作风格（model_identifier === 'generic_v1'）
					const filterGeneric = style => style && style.model_identifier !== 'generic_v1';
					const popularStyles = popularStylesRaw.filter(filterGeneric);
					const allStyles = allStylesRaw.filter(filterGeneric);

					this.styles = popularStyles;

					// 如果styles有数据，则更新aiTools（顶部横向滚动区域）
					if (popularStyles && popularStyles.length > 0) {
						this.aiTools = popularStyles.map(style => ({
								id: style.id,
								name: style.name,
								desc: style.description,
								image: style.cover_image_url || '/static/logo.png'
						}));
					}

					// 处理瀑布流区域：随机顺序展示全部风格（包括热门）
					const shuffledStyles = allStyles.slice().sort(() => Math.random() - 0.5);
					// 更新瀑布流数据
					this.initWaterfall(shuffledStyles);
				} catch (err) {
					console.error('获取风格列表失败', err);
					throw err; // 向上传递错误
				}
			},
			
			// 处理Banner点击
			onBannerClick(bannerId) {
				request.post(`/wowpic/banners/${bannerId}/click`, null, { showLoading: false }).then(res => {
					if (res.type === 'NAVIGATE') {
						let targetUrl = res.payload.url;
						// 如果链接只带了 style=xxx 而没有 styleId，尝试补充 styleId
						if (targetUrl.includes('style=') && !targetUrl.includes('styleId=')) {
							try {
								// 提取风格名称（解码避免中文被编码）
								const nameMatch = /style=([^&]+)/.exec(targetUrl);
								if (nameMatch && nameMatch[1]) {
									const styleName = decodeURIComponent(nameMatch[1]);
									// 在已加载的 styles 列表中查找对应风格
									const found = this.styles.find(s => s.name === styleName);
									if (found && found.id) {
										// 重新拼接 URL，带上 styleId
										targetUrl = `/pages/generate/generate?styleId=${found.id}&style=${encodeURIComponent(styleName)}`;
									}
								}
							} catch (e) {
								console.error('解析Banner跳转链接失败', e);
							}
						}
						uni.navigateTo({
							url: targetUrl
						});
					} else if (res.type === 'REWARD_COINS') {
						// 先显示成功提示
						uni.showToast({
							title: res.payload.message,
							icon: 'success'
						});
						
						// 领取后强制刷新用户信息，确保金币数量最新
						this.loadUserInfo(true).then(() => {
							// 强制更新视图（理论上 userInfo 已变更会自动更新，这里双保险）
							this.$forceUpdate();
							console.log('用户信息已更新，哇图币：', this.userInfo.coins);
						}).catch(err => {
							console.error('更新用户信息失败', err);
						});
					} else if (res.type === 'ALREADY_REWARDED') {
						uni.showToast({
							title: res.payload.message,
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('处理Banner点击失败', err);
				});
			},
			
			// 导航到生成页面
			navigateToGenerate(style) {
				uni.navigateTo({
					url: `/pages/generate/generate?styleId=${style.id}&style=${style.name}`
				});
			},
			
			onSwiperChange(e) {
				// 更新当前显示的banner索引
				this.currentBannerIndex = e.detail.current
			}
		}
	}
</script>

<style>
	.container {
		height: 100vh;
		padding: 0 24rpx;
		background-color: #F8F8F8;
	}
	
	/* 移动端禁用滚动条 */
	::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	/* 安全区域 */
	.safe-area {
		height: 88rpx;
	}
	
	.bottom-safe-area {
		height: 30rpx;
	}
	
	/* 头部用户区域样式 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
		padding: 0 6rpx;
	}
	
	.user-info {
		display: flex;
		align-items: center;
	}
	
	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 16rpx;
		border: 3rpx solid #FFF;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);
	}
	
	.greeting {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}
	
	/* 区块容器 */
	.section-container {
		margin-bottom: 20rpx;
		position: relative;
	}
	
	/* 精美的区块标题设计 */
	.fancy-section-header {
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: column;
		position: relative;
		padding-left: 20rpx;
	}
	
	.fancy-line {
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: linear-gradient(to bottom, #4A90E2, #6AC1E5);
		border-radius: 4rpx;
	}
	
	.fancy-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		line-height: 1.2;
	}
	
	.alt-color .fancy-line {
		background: linear-gradient(to bottom, #FF9AC9, #FF5BAF);
	}
	
	.alt-color .fancy-title {
		color: #444;
	}
	
	/* 顶部横向滚动风格样式 */
	.style-scroll {
		white-space: nowrap;
		margin: 0 -24rpx 0 -24rpx;
		padding: 0 24rpx;
	}
	
	.style-scroll-content {
		display: inline-flex;
		padding: 10rpx 0;
	}
	
	.scroll-style-card {
		position: relative;
		width: 280rpx;
		height: 380rpx;
		margin-right: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
	}
	
	.scroll-style-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s;
	}
	
	.scroll-style-card:active .scroll-style-image {
		transform: scale(1.05);
	}
	
	.scroll-style-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 40%;
		background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
	}
	
	.scroll-style-name {
		position: absolute;
		bottom: 20rpx;
		left: 0;
		right: 0;
		text-align: center;
		color: #FFFFFF;
		font-size: 28rpx;
		font-weight: bold;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		padding: 0 16rpx;
	}
	
	/* 中部mini banner样式 */
	.mini-banner {
		position: relative;
		margin-bottom: 0;
		height: 160rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.08);
	}
	
	.mini-banner-swiper {
		width: 100%;
		height: 100%;
	}
	
	.mini-banner-content {
		display: flex;
		align-items: center;
		width: 100%;
		height: 100%;
		background-size: cover;
		background-position: center;
		padding-left: 30rpx;
		/* 移除内联样式，添加遮罩渐变效果 */
		position: relative;
	}
	
	.mini-banner-content::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.5) 35%, rgba(0,0,0,0.2) 65%, rgba(0,0,0,0) 90%);
		pointer-events: none; /* 确保点击事件能穿透到下面的元素 */
	}
	
	.mini-banner-text {
		flex: 1;
		padding: 0 20rpx;
		color: #FFFFFF;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
		position: relative; /* 使文字位于遮罩之上 */
		z-index: 1;
	}
	
	.mini-banner-title {
		font-size: 32rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.mini-banner-subtitle {
		font-size: 24rpx;
		opacity: 0.9;
		display: block;
	}
	
	.mini-banner-indicators {
		position: absolute;
		bottom: 16rpx;
		right: 16rpx;
		display: flex;
		z-index: 10;
	}
	
	.mini-indicator {
		width: 12rpx;
		height: 4rpx;
		background-color: rgba(255, 255, 255, 0.5);
		margin: 0 3rpx;
		transition: all 0.2s;
		border-radius: 2rpx;
	}
	
	.mini-indicator.active {
		width: 24rpx;
		background-color: #FFFFFF;
	}
	
	/* 瀑布流样式 */
	.style-waterfall {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0;
	}
	
	.waterfall-column {
		width: 48%;
	}
	
	.waterfall-item {
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
		background-color: #FFFFFF;
	}
	
	.waterfall-image {
		width: 100%;
		display: block;
		object-fit: contain; /* 默认完整显示 */
		background-color: #f5f5f5;
	}
	
	.waterfall-image.crop {
		aspect-ratio: 0.75; /* 高度基准 3:4 */
		object-fit: cover;  /* 填满并裁剪 */
	}
	
	.waterfall-info {
		padding: 16rpx;
		background: #FFFFFF;
	}
	
	.waterfall-desc {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
		display: block;
	}
	
	.waterfall-name {
		font-size: 22rpx;
		color: #999;
		display: block;
	}

	.waterfall-hot {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		background: linear-gradient(135deg, #FF3A3A, #FF8E53);
		color: #FFFFFF;
		font-size: 22rpx;
		font-weight: bold;
		padding: 6rpx 14rpx;
		border-radius: 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(255,58,58,0.5);
		transform: rotate(5deg) scale(1.05);
		z-index: 2;
		animation: pulse-hot 1.5s infinite alternate;
	}

	.hot-text {
		display: inline-block;
		transform: scale(1);
		letter-spacing: 1rpx;
		text-shadow: 1rpx 1rpx 3rpx rgba(0,0,0,0.2);
	}
	
	@keyframes pulse-hot {
		0% {
			transform: rotate(5deg) scale(1.05);
		}
		100% {
			transform: rotate(5deg) scale(1.12);
		}
	}

	.waterfall-swiper {
		width: 100%;
		height: auto;
		aspect-ratio: 0.75; /* 保持图片比例，可根据实际需求调整 */
	}

	.waterfall-swiper .uni-swiper-dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		margin: 0 4rpx;
	}
</style>
